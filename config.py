import os
import json
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class STUNTURNConfig:
    """STUN/TURN服务器配置管理类"""
    
    def __init__(self):
        self.stun_servers = self._load_stun_servers()
        self.turn_servers = self._load_turn_servers()
    
    def _load_stun_servers(self) -> List[str]:
        """从环境变量加载STUN服务器列表"""
        stun_env = os.getenv('STUN_SERVERS', '')
        if not stun_env:
            logger.warning("未设置STUN_SERVERS环境变量，将使用空列表")
            return []
        
        try:
            # 支持JSON格式或逗号分隔格式
            if stun_env.startswith('['):
                return json.loads(stun_env)
            else:
                return [url.strip() for url in stun_env.split(',') if url.strip()]
        except json.JSONDecodeError as e:
            logger.error(f"解析STUN_SERVERS环境变量失败: {e}")
            return []
    
    def _load_turn_servers(self) -> List[Dict]:
        """从环境变量加载TURN服务器列表"""
        turn_env = os.getenv('TURN_SERVERS', '')
        if not turn_env:
            logger.warning("未设置TURN_SERVERS环境变量，将使用空列表")
            return []
        
        try:
            return json.loads(turn_env)
        except json.JSONDecodeError as e:
            logger.error(f"解析TURN_SERVERS环境变量失败: {e}")
            return []
    
    def get_client_ice_servers(self) -> List[Dict]:
        """获取客户端ICE服务器配置"""
        ice_servers = []

        # 添加STUN服务器
        for stun_url in self.stun_servers:
            ice_servers.append({"urls": [stun_url]})

        # 添加TURN服务器
        for turn_config in self.turn_servers:
            ice_servers.append({
                "urls": turn_config.get("urls", []),
                "username": turn_config.get("username"),
                "credential": turn_config.get("credential")
            })

        logger.info(f"客户端ICE服务器配置: {len(ice_servers)}个服务器")
        return ice_servers
    
    def get_server_ice_servers(self) -> List:
        """获取服务端ICE服务器配置 (aiortc RTCIceServer格式)"""
        from aiortc import RTCIceServer

        ice_servers = []

        # 添加STUN服务器
        if self.stun_servers:
            ice_servers.append(RTCIceServer(urls=self.stun_servers))

        # 添加TURN服务器
        for turn_config in self.turn_servers:
            ice_servers.append(RTCIceServer(
                urls=turn_config.get("urls", []),
                username=turn_config.get("username"),
                credential=turn_config.get("credential")
            ))

        logger.info(f"服务端ICE服务器配置: {len(ice_servers)}个服务器 (STUN: {len(self.stun_servers)}, TURN: {len(self.turn_servers)})")
        return ice_servers
    
    def is_configured(self) -> bool:
        """检查是否已配置STUN/TURN服务器"""
        return len(self.stun_servers) > 0 or len(self.turn_servers) > 0
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要信息"""
        return {
            "stun_count": len(self.stun_servers),
            "turn_count": len(self.turn_servers),
            "stun_servers": self.stun_servers,
            "turn_servers": [
                {
                    "urls": turn.get("urls", []),
                    "username": turn.get("username", ""),
                    "has_credential": bool(turn.get("credential"))
                }
                for turn in self.turn_servers
            ]
        }

# 全局配置实例
stun_turn_config = STUNTURNConfig()

def get_stun_turn_config() -> STUNTURNConfig:
    """获取STUN/TURN配置实例"""
    return stun_turn_config

def reload_config():
    """重新加载配置"""
    global stun_turn_config
    stun_turn_config = STUNTURNConfig()
    logger.info("STUN/TURN配置已重新加载")
