// DOM元素
var dataChannelLog = document.getElementById('data-channel'),
    iceConnectionLog = document.getElementById('ice-connection-state'),
    iceGatheringLog = document.getElementById('ice-gathering-state'),
    signalingLog = document.getElementById('signaling-state'),
    connectionLog = document.getElementById('connection-state');

// 全局变量
var pc = null;
var dc = null, dcInterval = null;
var localStream = null;
var currentStreamId = null;

// 日志函数
function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    
    if (dataChannelLog) {
        dataChannelLog.textContent += logMessage + '\n';
        dataChannelLog.scrollTop = dataChannelLog.scrollHeight;
    }
}

// 创建PeerConnection
async function createPeerConnection() {
    var config = {
        sdpSemantics: 'unified-plan'
    };

    // 检测是否为真正的localhost环境（更严格的检测）
    const serverUrl = document.getElementById('server-url').value;
    const isRealLocalhost = (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1') &&
                           serverUrl.includes('localhost');

    if (document.getElementById('use-stun').checked) {
        // 总是尝试从服务器获取ICE服务器配置
        try {
            const response = await fetch(`${serverUrl}/ice-config`);
            if (response.ok) {
                const iceConfig = await response.json();
                config.iceServers = iceConfig.iceServers || [];
                addLog(`🌐 从服务器获取ICE配置: ${config.iceServers.length}个服务器`);
                if (iceConfig.isLocalhost) {
                    addLog('🏠 服务器检测到localhost环境');
                } else {
                    addLog('🌍 服务器检测到广域网环境，使用STUN/TURN');
                }
            } else {
                addLog('⚠️ 无法获取服务器ICE配置，使用空配置');
                config.iceServers = [];
            }
        } catch (error) {
            addLog('❌ 获取ICE配置失败: ' + error.message);
            // 广域网环境下提供备用配置
            if (!isRealLocalhost) {
                addLog('🔄 使用备用STUN服务器');
                config.iceServers = [
                    { urls: ['stun:stun.l.google.com:19302'] },
                    { urls: ['stun:stun.cloudflare.com:3478'] }
                ];
            } else {
                config.iceServers = [];
            }
        }
    } else {
        // 未启用STUN时使用空配置
        config.iceServers = [];
        addLog('📴 STUN/TURN已禁用，使用空ICE配置');
    }

    pc = new RTCPeerConnection(config);

    // 注册状态监听器
    pc.addEventListener('icegatheringstatechange', () => {
        iceGatheringLog.textContent = pc.iceGatheringState;
        addLog(`ICE收集状态: ${pc.iceGatheringState}`);
    });

    pc.addEventListener('iceconnectionstatechange', () => {
        iceConnectionLog.textContent = pc.iceConnectionState;
        addLog(`ICE连接状态: ${pc.iceConnectionState}`);
        
        if (pc.iceConnectionState === 'connected') {
            addLog('🎉 WebRTC连接建立成功！', 'success');
        } else if (pc.iceConnectionState === 'failed') {
            addLog('❌ WebRTC连接失败', 'error');
        }
    });

    pc.addEventListener('signalingstatechange', () => {
        signalingLog.textContent = pc.signalingState;
        addLog(`信令状态: ${pc.signalingState}`);
    });

    pc.addEventListener('connectionstatechange', () => {
        connectionLog.textContent = pc.connectionState;
        addLog(`连接状态: ${pc.connectionState}`);
        
        if (pc.connectionState === 'connected') {
            addLog('✅ 媒体流传输正常');
            updateStreamInfo();
        }
    });

    // 监听远程音频/视频流
    pc.addEventListener('track', (evt) => {
        addLog(`📺 接收到远程${evt.track.kind}轨道`);
        if (evt.track.kind == 'video') {
            document.getElementById('remote-video').srcObject = evt.streams[0];
        } else {
            document.getElementById('remote-audio').srcObject = evt.streams[0];
        }
    });

    return pc;
}

// 枚举输入设备
function enumerateInputDevices() {
    const populateSelect = (select, devices) => {
        let counter = 1;
        devices.forEach((device) => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.text = device.label || ('设备 #' + counter);
            select.appendChild(option);
            counter += 1;
        });
    };

    navigator.mediaDevices.enumerateDevices().then((devices) => {
        populateSelect(
            document.getElementById('audio-input'),
            devices.filter((device) => device.kind == 'audioinput')
        );
        populateSelect(
            document.getElementById('video-input'),
            devices.filter((device) => device.kind == 'videoinput')
        );
    }).catch((e) => {
        addLog('❌ 枚举设备失败: ' + e.message);
    });
}

// 等待ICE收集完成
function waitForIceGathering() {
    return new Promise((resolve) => {
        if (pc.iceGatheringState === 'complete') {
            addLog('✅ ICE收集已完成');
            resolve();
            return;
        }
        
        // 检测是否为真正的localhost环境
        const serverUrl = document.getElementById('server-url').value;
        const isRealLocalhost = (window.location.hostname === 'localhost' ||
                                window.location.hostname === '127.0.0.1') &&
                               serverUrl.includes('localhost');
        const timeout = isRealLocalhost ? 2000 : 15000; // localhost用2秒，广域网用15秒
        
        let timeoutId = setTimeout(() => {
            pc.removeEventListener('icegatheringstatechange', checkState);
            addLog(`⏰ ICE收集超时(${timeout}ms)，继续进行协商`);
            resolve();
        }, timeout);
        
        function checkState() {
            if (pc.iceGatheringState === 'complete') {
                clearTimeout(timeoutId);
                pc.removeEventListener('icegatheringstatechange', checkState);
                addLog('✅ ICE收集完成');
                resolve();
            }
        }
        
        pc.addEventListener('icegatheringstatechange', checkState);
    });
}

// 协商连接
function negotiate() {
    addLog('🔄 开始WebRTC协商...');
    
    return pc.createOffer().then((offer) => {
        return pc.setLocalDescription(offer);
    }).then(() => {
        // 等待ICE收集完成
        addLog('⏳ 等待ICE候选者收集完成...');
        return waitForIceGathering();
    }).then(() => {
        var offer = pc.localDescription;
        var codec;

        // 应用音频编解码器选择
        codec = document.getElementById('audio-codec').value;
        if (codec !== 'default') {
            offer.sdp = sdpFilterCodec('audio', codec, offer.sdp);
        }

        // 应用视频编解码器选择
        codec = document.getElementById('video-codec').value;
        if (codec !== 'default') {
            offer.sdp = sdpFilterCodec('video', codec, offer.sdp);
        }

        document.getElementById('offer-sdp').textContent = offer.sdp;
        
        // 获取AI处理选项
        const videoTransform = document.getElementById('video-transform').value;
        const serverUrl = document.getElementById('server-url').value;
        
        addLog(`📤 发送offer到服务器 (AI处理: ${videoTransform})`);
        
        // 发送到服务器
        return fetch(`${serverUrl}/offer`, {
            body: JSON.stringify({
                sdp: offer.sdp,
                type: offer.type,
                video_transform: videoTransform
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            method: 'POST'
        });
    }).then((response) => {
        if (!response.ok) {
            throw new Error(`服务器响应错误: ${response.status}`);
        }
        return response.json();
    }).then((answer) => {
        addLog('📥 收到服务器answer');
        document.getElementById('answer-sdp').textContent = answer.sdp;
        return pc.setRemoteDescription(answer);
    }).then(() => {
        addLog('✅ WebRTC协商完成');
    }).catch((e) => {
        addLog('❌ 协商失败: ' + e.message);
        throw e;
    });
}

// 更新流信息显示
function updateStreamInfo() {
    const videoTransform = document.getElementById('video-transform').value;
    const aiEnabled = videoTransform !== 'none';
    
    document.getElementById('stream-id').textContent = currentStreamId || '本地生成';
    document.getElementById('ai-enabled').textContent = aiEnabled ? `是 (${videoTransform})` : '否';
    document.getElementById('stream-info').style.display = 'block';
}

// 复制流信息
function copyStreamInfo() {
    const info = `
流ID: ${document.getElementById('stream-id').textContent}
AI处理: ${document.getElementById('ai-enabled').textContent}
连接类型: ${document.getElementById('connection-type').textContent}
ICE状态: ${pc.iceConnectionState}
连接状态: ${pc.connectionState}
    `.trim();
    
    navigator.clipboard.writeText(info).then(() => {
        addLog('📋 流信息已复制到剪贴板');
    }).catch(() => {
        addLog('❌ 复制失败');
    });
}

// 开始连接
async function start() {
    document.getElementById('start').style.display = 'none';
    addLog('🚀 开始初始化WebRTC连接...');

    try {
        pc = await createPeerConnection();
        currentStreamId = generateStreamId();
    } catch (error) {
        addLog('❌ 创建连接失败: ' + error.message);
        document.getElementById('start').style.display = 'inline-block';
        return;
    }

    var time_start = null;

    const current_stamp = () => {
        if (time_start === null) {
            time_start = new Date().getTime();
            return 0;
        } else {
            return new Date().getTime() - time_start;
        }
    };

    // 设置数据通道
    if (document.getElementById('use-datachannel').checked) {
        var parameters = JSON.parse(document.getElementById('datachannel-parameters').value);

        dc = pc.createDataChannel('chat', parameters);
        dc.addEventListener('close', () => {
            clearInterval(dcInterval);
            addLog('📴 数据通道关闭');
        });
        dc.addEventListener('open', () => {
            addLog('📡 数据通道打开');
            dcInterval = setInterval(() => {
                var message = 'ping ' + current_stamp();
                addLog('> ' + message);
                dc.send(message);
            }, 1000);
        });
        dc.addEventListener('message', (evt) => {
            addLog('< ' + evt.data);

            if (evt.data.substring(0, 4) === 'pong') {
                var elapsed_ms = current_stamp() - parseInt(evt.data.substring(5), 10);
                addLog(`📊 RTT ${elapsed_ms} ms`);
            }
        });
    }

    // 构建媒体约束
    const constraints = {
        audio: false,
        video: false
    };

    if (document.getElementById('use-audio').checked) {
        const audioConstraints = {};

        const device = document.getElementById('audio-input').value;
        if (device) {
            audioConstraints.deviceId = { exact: device };
        }

        constraints.audio = Object.keys(audioConstraints).length ? audioConstraints : true;
    }

    if (document.getElementById('use-video').checked) {
        const videoConstraints = {};

        const device = document.getElementById('video-input').value;
        if (device) {
            videoConstraints.deviceId = { exact: device };
        }

        const resolution = document.getElementById('video-resolution').value;
        if (resolution) {
            const dimensions = resolution.split('x');
            videoConstraints.width = parseInt(dimensions[0], 0);
            videoConstraints.height = parseInt(dimensions[1], 0);
        }

        constraints.video = Object.keys(videoConstraints).length ? videoConstraints : true;
    }

    // 获取媒体并开始协商
    if (constraints.audio || constraints.video) {
        if (constraints.video) {
            document.getElementById('media').style.display = 'block';
        }
        
        addLog('🎥 请求用户媒体权限...');
        navigator.mediaDevices.getUserMedia(constraints).then((stream) => {
            localStream = stream;
            document.getElementById('local-video').srcObject = stream;
            
            addLog(`📹 获取媒体成功 (音频: ${constraints.audio ? '是' : '否'}, 视频: ${constraints.video ? '是' : '否'})`);
            
            stream.getTracks().forEach((track) => {
                pc.addTrack(track, stream);
            });
            return negotiate();
        }).then(() => {
            document.getElementById('stop').style.display = 'inline-block';
            addLog('🎉 连接建立完成！');
        }).catch((err) => {
            addLog('❌ 无法获取媒体: ' + err.message);
            document.getElementById('start').style.display = 'inline-block';
        });
    } else {
        // 仅数据通道连接
        negotiate().then(() => {
            document.getElementById('stop').style.display = 'inline-block';
            addLog('🎉 数据通道连接建立完成！');
        }).catch((err) => {
            addLog('❌ 连接失败: ' + err.message);
            document.getElementById('start').style.display = 'inline-block';
        });
    }
}

// 停止连接
function stop() {
    document.getElementById('stop').style.display = 'none';
    addLog('🛑 停止连接...');

    // 关闭数据通道
    if (dc) {
        dc.close();
        dc = null;
    }
    
    if (dcInterval) {
        clearInterval(dcInterval);
        dcInterval = null;
    }

    // 关闭收发器
    if (pc && pc.getTransceivers) {
        pc.getTransceivers().forEach((transceiver) => {
            if (transceiver.stop) {
                transceiver.stop();
            }
        });
    }

    // 停止本地媒体流
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
    }

    // 清除媒体元素
    document.getElementById('local-video').srcObject = null;
    document.getElementById('remote-video').srcObject = null;
    document.getElementById('remote-audio').srcObject = null;

    // 关闭对等连接
    if (pc) {
        setTimeout(() => {
            pc.close();
            pc = null;
        }, 500);
    }

    // 隐藏流信息
    document.getElementById('stream-info').style.display = 'none';
    
    document.getElementById('start').style.display = 'inline-block';
    addLog('✅ 连接已关闭');
}

// SDP编解码器过滤
function sdpFilterCodec(kind, codec, realSdp) {
    var allowed = []
    var rtxRegex = new RegExp('a=fmtp:(\\d+) apt=(\\d+)\r$');
    var codecRegex = new RegExp('a=rtpmap:([0-9]+) ' + escapeRegExp(codec))
    var videoRegex = new RegExp('(m=' + kind + ' .*?)( ([0-9]+))*\\s*$')

    var lines = realSdp.split('\n');

    var isKind = false;
    for (var i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('m=' + kind + ' ')) {
            isKind = true;
        } else if (lines[i].startsWith('m=')) {
            isKind = false;
        }

        if (isKind) {
            var match = lines[i].match(codecRegex);
            if (match) {
                allowed.push(parseInt(match[1]));
            }

            match = lines[i].match(rtxRegex);
            if (match && allowed.includes(parseInt(match[2]))) {
                allowed.push(parseInt(match[1]));
            }
        }
    }

    var skipRegex = 'a=(fmtp|rtcp-fb|rtpmap):([0-9]+)';
    var sdp = '';

    isKind = false;
    for (var i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('m=' + kind + ' ')) {
            isKind = true;
        } else if (lines[i].startsWith('m=')) {
            isKind = false;
        }

        if (isKind) {
            var skipMatch = lines[i].match(skipRegex);
            if (skipMatch && !allowed.includes(parseInt(skipMatch[2]))) {
                continue;
            } else if (lines[i].match(videoRegex)) {
                sdp += lines[i].replace(videoRegex, '$1 ' + allowed.join(' ')) + '\n';
            } else {
                sdp += lines[i] + '\n';
            }
        } else {
            sdp += lines[i] + '\n';
        }
    }

    return sdp;
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function generateStreamId() {
    return 'stream-' + Math.random().toString(36).substr(2, 9);
}

// 自动设置服务器URL
function autoSetServerUrl() {
    const serverUrlInput = document.getElementById('server-url');
    if (!serverUrlInput.value) {
        // 根据当前页面URL自动设置服务器地址
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        const port = window.location.port;

        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            // localhost环境
            serverUrlInput.value = `${protocol}//${hostname}:8090`;
            addLog('🏠 自动设置为localhost服务器地址');
        } else {
            // 广域网环境，假设服务器在同一域名的8090端口
            const serverPort = port ? (port === '80' || port === '443' ? '8090' : '8090') : '8090';
            serverUrlInput.value = `${protocol}//${hostname}:${serverPort}`;
            addLog('🌍 自动设置为广域网服务器地址');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    addLog('📱 页面加载完成，正在初始化...');

    // 自动设置服务器URL
    autoSetServerUrl();

    // 枚举设备
    enumerateInputDevices();

    // 初始化状态显示
    iceGatheringLog.textContent = 'new';
    iceConnectionLog.textContent = 'new';
    signalingLog.textContent = 'stable';
    connectionLog.textContent = 'new';

    addLog('✅ 初始化完成，可以开始测试');
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (pc) {
        pc.close();
    }
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
    }
});