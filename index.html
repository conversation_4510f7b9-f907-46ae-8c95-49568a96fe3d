<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC AI Demo - 完整测试</title>
    <style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
    }

    .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        max-width: 1400px;
    }

    button {
        padding: 10px 20px;
        margin: 5px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
    }

    .start-btn {
        background-color: #4CAF50;
        color: white;
    }

    .stop-btn {
        background-color: #f44336;
        color: white;
        display: none;
    }

    button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    pre {
        overflow-x: hidden;
        overflow-y: auto;
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        max-height: 150px;
        font-size: 12px;
    }

    video {
        width: 100%;
        max-width: 400px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background: #000;
    }

    audio {
        width: 100%;
        max-width: 400px;
    }

    .option {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .option label {
        min-width: 120px;
        font-weight: bold;
    }

    select, input[type="text"] {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 150px;
    }

    #media {
        display: none;
    }

    .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 20px 0;
    }

    .status-item {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .status-item strong {
        color: #495057;
    }

    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }

    .ai-controls {
        background: #e7f3ff;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
    }

    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .stream-info {
        background: #d4edda;
        padding: 15px;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }

    .controls-row {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
        margin: 10px 0;
    }

    h2 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 5px;
    }

    h3 {
        color: #495057;
        margin-top: 20px;
    }

    .section {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        background: #fff;
    }
    </style>
</head>
<body>

<div class="container">
    <h1>🎥 WebRTC AI Processing Demo</h1>
    <p>完整的WebRTC测试环境 - 仿照aiortc example/server实现</p>
    
    <div class="ai-controls">
        <h3>📡 服务器端点信息</h3>
        <p><strong>WHIP推流端点:</strong> <code>POST {SERVER_URL}/whip</code></p>
        <p><strong>WHEP拉流端点:</strong> <code>POST {SERVER_URL}/whep/{stream_id}[?transform=TYPE]</code></p>
        <p style="margin-left: 20px; font-size: 14px;">
            支持的transform类型: <code>pose</code> (YOLO姿态检测), <code>edges</code> (边缘检测),
            <code>cartoon</code> (卡通效果), <code>rotate</code> (旋转), <code>none</code> (无处理)
        </p>
        <p><strong>双向通信端点:</strong> <code>POST {SERVER_URL}/offer</code> (本页面使用)</p>
        <p><strong>ICE配置端点:</strong> <code>GET {SERVER_URL}/ice-config</code></p>
        <p><strong>流列表API:</strong> <code>GET {SERVER_URL}/streams</code></p>
        <p style="margin-top: 10px; font-size: 14px; color: #666;">
            💡 <strong>广域网使用说明:</strong> 将 <code>{SERVER_URL}</code> 替换为你的实际服务器地址，如 <code>http://your-domain.com:8090</code>
        </p>
        <p><a href="/api" target="_blank" style="color: #007bff; text-decoration: none;">📖 查看完整API文档</a></p>
    </div>
    
    <div class="section">
        <h2>配置选项</h2>
        
        <div class="option">
            <input id="use-datachannel" checked="checked" type="checkbox"/>
            <label for="use-datachannel">使用数据通道</label>
            <select id="datachannel-parameters">
                <option value='{"ordered": true}'>有序，可靠</option>
                <option value='{"ordered": false, "maxRetransmits": 0}'>无序，无重传</option>
                <option value='{"ordered": false, "maxPacketLifetime": 500}'>无序，500ms生命周期</option>
            </select>
        </div>

        <div class="option">
            <input id="use-audio" checked="checked" type="checkbox"/>
            <label for="use-audio">使用音频</label>
            <select id="audio-input">
                <option value="" selected>默认设备</option>
            </select>
            <select id="audio-codec">
                <option value="default" selected>默认编解码器</option>
                <option value="opus/48000/2">Opus</option>
                <option value="G722/8000">G722</option>
                <option value="PCMU/8000">PCMU</option>
                <option value="PCMA/8000">PCMA</option>
            </select>
        </div>

        <div class="option">
            <input id="use-video" checked="checked" type="checkbox"/>
            <label for="use-video">使用视频</label>
            <select id="video-input">
                <option value="" selected>默认设备</option>
            </select>
            <select id="video-resolution">
                <option value="" selected>默认分辨率</option>
                <option value="320x240">320x240</option>
                <option value="640x480">640x480</option>
                <option value="960x540">960x540</option>
                <option value="1280x720">1280x720</option>
            </select>
            <select id="video-codec">
                <option value="default" selected>默认编解码器</option>
                <option value="VP8/90000">VP8</option>
                <option value="H264/90000">H264</option>
            </select>
        </div>

        <div class="ai-controls">
            <label>🤖 AI视频处理:</label>
            <select id="video-transform">
                <option value="none">无处理</option>
                <option value="pose" selected>姿态检测 (YOLO)</option>
                <option value="edges">边缘检测</option>
                <option value="cartoon">卡通效果</option>
                <option value="rotate">旋转</option>
            </select>
        </div>

        <div class="option">
            <input id="use-stun" checked type="checkbox"/>
            <label for="use-stun">使用STUN服务器</label>
        </div>

        <div class="controls-row">
            <button id="start" class="start-btn" onclick="start()">开始连接</button>
            <button id="stop" class="stop-btn" onclick="stop()">停止连接</button>
            <input type="text" id="server-url" value="" placeholder="服务器地址 (如: http://your-domain.com:8090)">
        </div>
    </div>
</div>

<!-- 连接状态 -->
<div class="container">
    <h2>连接状态</h2>
    <div class="status-grid">
        <div class="status-item">
            <strong>ICE收集状态:</strong> <span id="ice-gathering-state">new</span>
        </div>
        <div class="status-item">
            <strong>ICE连接状态:</strong> <span id="ice-connection-state">new</span>
        </div>
        <div class="status-item">
            <strong>信令状态:</strong> <span id="signaling-state">stable</span>
        </div>
        <div class="status-item">
            <strong>连接状态:</strong> <span id="connection-state">new</span>
        </div>
    </div>
</div>

<!-- 媒体显示 -->
<div class="container">
    <div id="media">
        <h2>媒体流</h2>
        <div class="media-grid">
            <div>
                <h3>本地视频</h3>
                <video id="local-video" autoplay muted playsinline></video>
            </div>
            <div>
                <h3>远程视频 (AI处理后)</h3>
                <video id="remote-video" autoplay playsinline></video>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>音频</h3>
            <audio id="remote-audio" autoplay></audio>
        </div>
    </div>
</div>

<!-- 流信息和数据通道 -->
<div class="container">
    <div class="info-grid">
        <div>
            <h2>流信息</h2>
            <div id="stream-info" class="stream-info" style="display: none;">
                <p><strong>推流ID:</strong> <span id="stream-id">-</span></p>
                <p><strong>AI处理:</strong> <span id="ai-enabled">-</span></p>
                <p><strong>连接类型:</strong> <span id="connection-type">双向通信</span></p>
                <button onclick="copyStreamInfo()">复制流信息</button>
            </div>
        </div>
        
        <div>
            <h2>数据通道</h2>
            <pre id="data-channel" style="height: 200px;"></pre>
        </div>
    </div>
</div>

<!-- SDP信息 -->
<div class="container">
    <h2>SDP信息</h2>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
            <h3>Offer SDP</h3>
            <pre id="offer-sdp" style="height: 300px;"></pre>
        </div>
        <div>
            <h3>Answer SDP</h3>
            <pre id="answer-sdp" style="height: 300px;"></pre>
        </div>
    </div>
</div>

<script src="client.js"></script>
</body>
</html>