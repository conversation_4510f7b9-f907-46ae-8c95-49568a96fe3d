"""
低延迟WebRTC配置管理
协调客户端和服务器端的延迟和缓存设置
"""

import logging

logger = logging.getLogger(__name__)

class LowLatencyConfig:
    """低延迟配置管理类"""
    
    def __init__(self):
        # 基础配置
        self.enabled = True
        
        # 码率配置 - 用于REMB反馈生成和带宽估计
        self.bitrate_config = {
            'target_bitrate': 3000000,  # 目标码率3.0Mbps，用于REMB反馈
            'min_bitrate': 1500000,     # 最低码率1.5Mbps，REMB下限
            'max_bitrate': 6000000,     # 最高码率6.0Mbps，REMB上限
        }

        # 服务器端缓冲区配置 - 优化卡顿恢复，减少预取延迟
        self.server_jitter_buffer = {
            'video': {
                'capacity': 32,             # 视频缓冲区容量32帧(1067ms@30fps) - 保持稳定性
                'prefetch': 1,              # 预取1帧(33ms@30fps) - 快速响应，减少卡顿恢复时间
                'max_delay': 100,           # 最大延迟100ms - 允许更多缓冲
                'target_delay': 50,         # 目标延迟50ms - 平衡延迟和稳定性
            },
            'audio': {
                'capacity': 16,             # 音频缓冲区容量16帧 - 增加音频稳定性
                'prefetch': 1,              # 预取1帧 - 快速响应，减少音频卡顿
                'max_delay': 50,            # 最大延迟50ms
                'target_delay': 25,         # 目标延迟25ms
            }
        }
        
        # 编码器配置 - 稳定兼容，减少解码错误
        self.encoder_config = {
            'h264': {
                'preset': 'fast',           # 快速预设，平衡编码速度和质量
                'tune': 'zerolatency',      # 零延迟调优，禁用B帧和前瞻
                'profile': 'baseline',      # 基线配置，最大兼容性
                'level': '3.1',             # 适合720p的级别
                'profile_level_id': '42E01F',  # Baseline Profile Level 3.1
                'packetization_mode': '1',  # 匹配客户端的packetization-mode
                'level_asymmetry_allowed': '1',  # 允许级别不对称
            },
            'vp8': {
                # VP8已在客户端完全禁用，但保留配置以备用
                'deadline': 'realtime',     # 实时编码
                'cpu_used': '8',            # 最快编码速度
                'static_thresh': '0',       # 禁用静态阈值
                'max_intra_rate': '300',    # 适配30fps的关键帧频率
                'kf_max_dist': '30',        # 关键帧最大距离1秒
                'kf_min_dist': '15',        # 关键帧最小距离0.5秒
            }
        }
        
        # 客户端配置 - TURN中继超低延迟优化
        self.client_config = {
            'ice_connection_timeout': 8000,        # ICE连接超时8秒（TURN连接更快）
            'ice_backup_ping_interval': 1000,      # 增加ping频率，快速检测连接
            'ice_inactive_timeout': 6000,          # 减少非活跃超时，快速重连
            'enable_hardware_encoder': True,
            'enable_h264_high_profile': False,     # 禁用高配置，减少延迟
            'enable_intel_vp8': False,             # 禁用Intel VP8
            'frame_drop_threshold': 0.05,          # 5%丢帧阈值，减少卡顿
        }
        
        # 网络配置 - TURN中继超低延迟优化
        self.network_config = {
            'bundle_policy': 'max-bundle',          # 最大化bundle策略
            'rtcp_mux_policy': 'require',           # 要求RTCP复用
            'tcp_candidate_policy': 'disabled',    # 禁用TCP候选，强制UDP减少延迟
            'continual_gathering_policy': 'gather_once',  # 单次收集，减少延迟
            'ice_restart_policy': 'normal',        # 正常ICE重启策略
            'dtls_srtp_key_agreement': 'enabled',  # 启用DTLS-SRTP密钥协商
        }
    
    def get_server_jitter_buffer_config(self):
        """获取服务器端JitterBuffer配置"""
        return {
            'video': {
                'capacity': self.server_jitter_buffer['video']['capacity'],
                'prefetch': self.server_jitter_buffer['video']['prefetch'],
                'is_video': True
            },
            'audio': {
                'capacity': self.server_jitter_buffer['audio']['capacity'],
                'prefetch': self.server_jitter_buffer['audio']['prefetch'],
                'is_video': False
            }
        }
    
    def get_h264_encoder_options(self):
        """获取H264编码器选项"""
        return self.encoder_config.get('h264', {})

    def get_vp8_encoder_options(self):
        """获取VP8编码器选项"""
        return self.encoder_config.get('vp8', {})
    
    def get_client_config_json(self):
        """获取客户端配置JSON（用于API返回）"""
        return {
            'lowLatencyEnabled': self.enabled,
            'bitrate': self.bitrate_config,  # 只返回码率配置，用于REMB反馈
            'client': self.client_config,
            'network': self.network_config
        }
    
    def log_configuration(self):
        """记录当前配置"""
        if self.enabled:
            logger.info("⚡ TURN中继超低延迟模式已启用")
        else:
            logger.info("📺 标准延迟模式")

# 全局配置实例
low_latency_config = LowLatencyConfig()

def get_low_latency_config():
    """获取低延迟配置实例"""
    return low_latency_config

def apply_server_optimizations():
    """应用服务器端优化"""
    config = get_low_latency_config()
    
    if not config.enabled:
        return
    
    # 这个函数将被主服务器调用来应用优化
    logger.info("🔧 应用服务器端低延迟优化...")
    
    # 返回配置供服务器使用
    return {
        'jitter_buffer': config.get_server_jitter_buffer_config(),
        'h264_options': config.get_h264_encoder_options(),
        'vp8_options': config.get_vp8_encoder_options()
    }

def get_client_optimization_config():
    """获取客户端优化配置"""
    config = get_low_latency_config()
    return config.get_client_config_json()
